import React, { useState } from 'react';
import { WelcomeScreen } from './components/WelcomeScreen';
import { HostInterface } from './components/HostInterface';
import { ParticipantInterface } from './components/ParticipantInterface';
import { ConnectionProvider } from './contexts/ConnectionContext';

type AppMode = 'welcome' | 'host' | 'participant';

const AppContent: React.FC = () => {
  const [mode, setMode] = useState<AppMode>('welcome');
  const [roomId, setRoomId] = useState<string>('');

  const handleJoinAsHost = (id: string) => {
    setRoomId(id);
    setMode('host');
  };

  const handleJoinAsParticipant = (id: string) => {
    setRoomId(id);
    setMode('participant');
  };

  return (
    <ConnectionProvider>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
        {mode === 'welcome' && (
          <WelcomeScreen
            onJoinAsHost={handleJoinAsHost}
            onJoinAsParticipant={handleJoinAsParticipant}
          />
        )}
        {mode === 'host' && (
          <HostInterface roomId={roomId} onLeave={() => setMode('welcome')} />
        )}
        {mode === 'participant' && (
          <ParticipantInterface roomId={roomId} onLeave={() => setMode('welcome')} />
        )}


      </div>
    </ConnectionProvider>
  );
};

function App() {
  return <AppContent />;
}

export default App;
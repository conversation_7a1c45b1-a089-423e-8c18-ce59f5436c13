/**
 * WebGL 2.0 rendering engine for video compositing
 */

import { vertexShaderSource, fragmentShaderSource } from './shaders';
import { Viewport } from './layoutUtils';

export class WebGLEngine {
  private gl: WebGL2RenderingContext | null = null;
  private program: WebGLProgram | null = null;
  private textures: Map<string, WebGLTexture> = new Map();

  constructor() {
  }

  /**
   * Initialize WebGL context and shaders
   */
  initialize(canvas: HTMLCanvasElement): boolean {
    const gl = canvas.getContext('webgl2');
    if (!gl) {
      console.error('❌ WebGL 2 not supported');
      return false;
    }

    this.gl = gl;

    const program = this.createProgram(gl);
    if (!program) return false;

    this.program = program;

    // Set up vertex buffer for a full-screen quad
    const positions = new Float32Array([
      -1, -1,  0, 0,  // bottom-left
       1, -1,  1, 0,  // bottom-right
      -1,  1,  0, 1,  // top-left
       1,  1,  1, 1,  // top-right
    ]);

    const positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

    // Set up vertex attributes
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const texCoordLocation = gl.getAttribLocation(program, 'a_texCoord');

    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 16, 0);

    gl.enableVertexAttribArray(texCoordLocation);
    gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 16, 8);

    // this.addLog('success', 'WebGL', 'WebGL 2 context initialized successfully');
    return true;
  }

  /**
   * Create shader program
   */
  private createProgram(gl: WebGL2RenderingContext): WebGLProgram | null {
    const vertexShader = this.createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = this.createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);

    if (!vertexShader || !fragmentShader) return null;

    const program = gl.createProgram();
    if (!program) return null;

    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error('❌ WebGL program linking error:', gl.getProgramInfoLog(program));
      gl.deleteProgram(program);
      return null;
    }

    // Clean up shaders
    gl.deleteShader(vertexShader);
    gl.deleteShader(fragmentShader);

    return program;
  }

  /**
   * Create individual shader
   */
  private createShader(gl: WebGL2RenderingContext, type: number, source: string): WebGLShader | null {
    const shader = gl.createShader(type);
    if (!shader) return null;

    gl.shaderSource(shader, source);
    gl.compileShader(shader);

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      // this.addLog('error', 'WebGL', `Shader compilation error: ${gl.getShaderInfoLog(shader)}`);
      gl.deleteShader(shader);
      return null;
    }

    return shader;
  }

  /**
   * Create texture for video element
   */
  createVideoTexture(videoId: string, video: HTMLVideoElement): WebGLTexture | null {
    if (!this.gl) return null;

    const texture = this.gl.createTexture();
    if (!texture) return null;

    this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);

    this.textures.set(videoId, texture);
    return texture;
  }

  /**
   * Create texture for image element
   */
  createImageTexture(imageId: string, image: HTMLImageElement): WebGLTexture | null {
    if (!this.gl) return null;

    const texture = this.gl.createTexture();
    if (!texture) {
      console.error(`❌ Failed to create WebGL texture for ${imageId}`);
      return null;
    }

    this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);

    try {
      // Upload the image to the texture
      this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, this.gl.RGBA, this.gl.UNSIGNED_BYTE, image);

      // Check for WebGL errors
      const error = this.gl.getError();
      if (error !== this.gl.NO_ERROR) {
        // this.addLog('error', 'WebGL', `WebGL error during texture upload for ${imageId}: ${error}`);
        this.gl.deleteTexture(texture);
        return null;
      }

      this.textures.set(imageId, texture);
      // this.addLog('success', 'WebGL', `Successfully created texture for ${imageId}`);
      return texture;
    } catch (error) {
      // this.addLog('error', 'WebGL', `Exception during texture creation for ${imageId}`, error);
      this.gl.deleteTexture(texture);
      return null;
    }
  }

  /**
   * Update video texture with current frame
   */
  updateVideoTexture(texture: WebGLTexture, video: HTMLVideoElement): void {
    if (!this.gl) return;

    this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
    this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, this.gl.RGBA, this.gl.UNSIGNED_BYTE, video);
  }

  /**
   * Get existing texture by ID
   */
  getTexture(id: string): WebGLTexture | undefined {
    return this.textures.get(id);
  }

  /**
   * Clear canvas with specified color
   */
  clear(r: number = 0, g: number = 0, b: number = 0, a: number = 1): void {
    if (!this.gl) return;

    this.gl.clearColor(r, g, b, a);
    this.gl.clear(this.gl.COLOR_BUFFER_BIT);
  }

  /**
   * Render video/image with specified viewport and styling
   */
  renderVideo(
    texture: WebGLTexture,
    viewport: Viewport,
    borderColor: [number, number, number] = [0, 0.83, 1],
    alpha: number = 1.0,
    borderWidth: number = 0.02
  ): void {
    if (!this.gl || !this.program) return;

    this.gl.useProgram(this.program);

    // Enable blending for proper alpha compositing
    this.gl.enable(this.gl.BLEND);
    this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA);

    // Set uniforms
    const textureLocation = this.gl.getUniformLocation(this.program, 'u_texture');
    const alphaLocation = this.gl.getUniformLocation(this.program, 'u_alpha');
    const resolutionLocation = this.gl.getUniformLocation(this.program, 'u_resolution');
    const viewportLocation = this.gl.getUniformLocation(this.program, 'u_viewport');
    const borderColorLocation = this.gl.getUniformLocation(this.program, 'u_borderColor');
    const borderWidthLocation = this.gl.getUniformLocation(this.program, 'u_borderWidth');

    if (textureLocation !== null) this.gl.uniform1i(textureLocation, 0);
    if (alphaLocation !== null) this.gl.uniform1f(alphaLocation, alpha);
    if (resolutionLocation !== null) this.gl.uniform2f(resolutionLocation, this.gl.canvas.width, this.gl.canvas.height);
    if (viewportLocation !== null) this.gl.uniform4f(viewportLocation,
      viewport.x / this.gl.canvas.width,
      viewport.y / this.gl.canvas.height,
      viewport.width / this.gl.canvas.width,
      viewport.height / this.gl.canvas.height
    );
    if (borderColorLocation !== null) this.gl.uniform3f(borderColorLocation, borderColor[0], borderColor[1], borderColor[2]);
    if (borderWidthLocation !== null) this.gl.uniform1f(borderWidthLocation, borderWidth);

    // Bind texture
    this.gl.activeTexture(this.gl.TEXTURE0);
    this.gl.bindTexture(this.gl.TEXTURE_2D, texture);

    // Check for WebGL errors
    const error = this.gl.getError();
    if (error !== this.gl.NO_ERROR) {
      console.error('WebGL error before draw:', error);
      return;
    }

    // Draw
    this.gl.drawArrays(this.gl.TRIANGLE_STRIP, 0, 4);

    // Check for errors after draw
    const drawError = this.gl.getError();
    if (drawError !== this.gl.NO_ERROR) {
      console.error('WebGL error after draw:', drawError);
    }
  }

  /**
   * Clean up all textures
   */
  cleanup(): void {
    if (!this.gl) return;

    this.textures.forEach((texture) => {
      this.gl!.deleteTexture(texture);
    });
    this.textures.clear();
  }

  /**
   * Get texture count for debugging
   */
  getTextureCount(): number {
    return this.textures.size;
  }

  /**
   * Get texture keys for debugging
   */
  getTextureKeys(): string[] {
    return Array.from(this.textures.keys());
  }
}
